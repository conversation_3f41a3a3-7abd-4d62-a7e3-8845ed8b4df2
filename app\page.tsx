'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Category } from '@/types/game';
import MainLayout from '@/components/layout/MainLayout';
import HeroSection from '@/components/landing/HeroSection';
import CategorySelector from '@/components/landing/CategorySelector';
import Button from '@/components/ui/Button';

export default function Home() {
  const [showCategorySelection, setShowCategorySelection] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const router = useRouter();

  const handlePlayNow = () => {
    setShowCategorySelection(true);
  };

  const handleCategorySelect = (category: Category) => {
    setSelectedCategory(category);
  };

  const handleStartGame = () => {
    if (selectedCategory) {
      // Store selected category in localStorage for the game page
      localStorage.setItem('selectedCategory', JSON.stringify(selectedCategory));
      router.push('/game');
    }
  };

  const handleBackToHero = () => {
    setShowCategorySelection(false);
    setSelectedCategory(null);
  };

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        {!showCategorySelection ? (
          <HeroSection onPlayNow={handlePlayNow} />
        ) : (
          <div className="max-w-6xl mx-auto">
            {/* Back Button */}
            <div className="mb-8">
              <Button
                variant="outline"
                onClick={handleBackToHero}
                className="mb-4"
              >
                ← Back
              </Button>
            </div>

            {/* Category Selection */}
            <CategorySelector
              onCategorySelect={handleCategorySelect}
              selectedCategory={selectedCategory}
            />

            {/* Start Game Button */}
            {selectedCategory && (
              <div className="text-center mt-8">
                <Button
                  onClick={handleStartGame}
                  size="lg"
                  className="px-8 py-4 text-xl"
                >
                  Start Game with {selectedCategory.name}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </MainLayout>
  );
}
