import React from 'react';
import { formatDistance, formatScore } from '@/utils/helpers';

interface ScoreDisplayProps {
  distance: number;
  score: number;
  maxScore: number;
  className?: string;
}

export default function ScoreDisplay({ 
  distance, 
  score, 
  maxScore,
  className = '' 
}: ScoreDisplayProps) {
  const scorePercentage = (score / maxScore) * 100;
  
  const getScoreColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    if (percentage >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreMessage = (percentage: number) => {
    if (percentage >= 90) return 'Excellent! 🎯';
    if (percentage >= 80) return 'Great job! 👏';
    if (percentage >= 60) return 'Good guess! 👍';
    if (percentage >= 40) return 'Not bad! 🤔';
    if (percentage >= 20) return 'Could be better 😅';
    return 'Keep trying! 💪';
  };

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 text-center ${className}`}>
      <h3 className="text-xl font-bold text-gray-800 mb-4">Round Result</h3>
      
      {/* Distance */}
      <div className="mb-4">
        <div className="text-3xl font-bold text-gray-800 mb-1">
          {formatDistance(distance)}
        </div>
        <div className="text-sm text-gray-600">
          away from the actual location
        </div>
      </div>

      {/* Score */}
      <div className="mb-4">
        <div className={`text-4xl font-bold mb-1 ${getScoreColor(scorePercentage)}`}>
          {formatScore(score)}
        </div>
        <div className="text-sm text-gray-600">
          out of {formatScore(maxScore)} points
        </div>
      </div>

      {/* Score Bar */}
      <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
        <div 
          className={`h-3 rounded-full transition-all duration-500 ${
            scorePercentage >= 80 ? 'bg-green-500' :
            scorePercentage >= 60 ? 'bg-yellow-500' :
            scorePercentage >= 40 ? 'bg-orange-500' :
            'bg-red-500'
          }`}
          style={{ width: `${scorePercentage}%` }}
        />
      </div>

      {/* Message */}
      <div className="text-lg font-semibold text-gray-700">
        {getScoreMessage(scorePercentage)}
      </div>
    </div>
  );
}
