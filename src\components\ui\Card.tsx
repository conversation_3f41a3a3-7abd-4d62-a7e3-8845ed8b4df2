import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  selected?: boolean;
}

export default function Card({ children, className = '', onClick, selected = false }: CardProps) {
  const baseClasses = 'bg-white rounded-lg shadow-md border transition-all duration-200';
  const interactiveClasses = onClick ? 'cursor-pointer hover:shadow-lg hover:scale-105' : '';
  const selectedClasses = selected ? 'ring-2 ring-blue-500 border-blue-500' : 'border-gray-200';
  
  const classes = `${baseClasses} ${interactiveClasses} ${selectedClasses} ${className}`;
  
  return (
    <div className={classes} onClick={onClick}>
      {children}
    </div>
  );
}
