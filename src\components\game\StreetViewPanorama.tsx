'use client';

import React, { useEffect, useState } from 'react';
import { Location } from '@/types/game';

interface StreetViewPanoramaProps {
  location: Location;
  onPanoramaLoad?: () => void;
  className?: string;
}

export default function StreetViewPanorama({
  location,
  onPanoramaLoad,
  className = ''
}: StreetViewPanoramaProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState<string>('');

  useEffect(() => {
    initializeStaticImage();
  }, [location]);

  const initializeStaticImage = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Validate location coordinates
      if (!location || typeof location.lat !== 'number' || typeof location.lng !== 'number') {
        throw new Error('Invalid location coordinates');
      }

      // Create a static satellite image URL using OpenStreetMap tiles
      // We'll use a higher zoom level to show more detail
      const zoom = 16;

      // Convert lat/lng to tile coordinates
      const tileX = Math.floor((location.lng + 180) / 360 * Math.pow(2, zoom));
      const tileY = Math.floor((1 - Math.log(Math.tan(location.lat * Math.PI / 180) + 1 / Math.cos(location.lat * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, zoom));

      // Use OpenStreetMap tile URL
      const url = `https://tile.openstreetmap.org/${zoom}/${tileX}/${tileY}.png`;
      setImageUrl(url);
      setIsLoading(false);
      onPanoramaLoad?.();

    } catch (err) {
      console.error('Error loading static image:', err);
      setError(err instanceof Error ? err.message : 'Failed to load location image');
      setIsLoading(false);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600">Loading location image...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center">
            <p className="text-red-600 mb-2">{error}</p>
            <button 
              onClick={initializeStaticImage}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      )}

      {/* Static Image Container */}
      {!isLoading && !error && imageUrl && (
        <div className="w-full h-full min-h-[400px] rounded-lg overflow-hidden bg-gray-200 flex items-center justify-center">
          <img
            src={imageUrl}
            alt="Location view"
            className="max-w-full max-h-full object-contain"
            onError={() => setError('Failed to load location image')}
          />
        </div>
      )}

      {/* Info */}
      {!isLoading && !error && (
        <div className="absolute top-4 right-4 z-10">
          <div className="px-3 py-2 bg-white bg-opacity-90 text-gray-700 rounded shadow text-xs">
            📍 Location: {location.lat.toFixed(4)}, {location.lng.toFixed(4)}
          </div>
        </div>
      )}
    </div>
  );
}
