import { GameSession, GameRound, Location, Category } from '@/types/game';
import { processRound, calculateTotalScore } from './gameLogic';
import { generateRandomStreetViewLocation } from './googleMaps';
import { GAME_CONFIG } from '@/utils/constants';

export class GameManager {
  private gameSession: GameSession;
  private startTime: number;

  constructor(category: Category, userId?: string) {
    this.gameSession = {
      userId,
      categoryId: category._id,
      startTime: new Date(),
      totalScore: 0,
      isCompleted: false,
      rounds: [],
    };
    this.startTime = Date.now();
  }

  /**
   * Start a new round
   * @returns The location for the new round
   */
  startNewRound(): Location {
    const roundNumber = this.gameSession.rounds.length + 1;
    
    if (roundNumber > GAME_CONFIG.TOTAL_ROUNDS) {
      throw new Error('Game already completed');
    }

    // Generate a new location for this round
    const panoramaLocation = generateRandomStreetViewLocation();
    
    // Create a new round
    const newRound: GameRound = {
      roundNumber,
      panoramaLocation,
      guessLocation: null,
      distanceKm: null,
      score: null,
      timeTakenMs: null,
    };

    this.gameSession.rounds.push(newRound);
    this.startTime = Date.now();

    return panoramaLocation;
  }

  /**
   * Submit a guess for the current round
   * @param guessLocation User's guessed location
   * @returns Processed round data
   */
  submitGuess(guessLocation: Location): GameRound {
    const currentRound = this.getCurrentRound();
    
    if (!currentRound) {
      throw new Error('No active round');
    }

    if (currentRound.guessLocation) {
      throw new Error('Guess already submitted for this round');
    }

    const timeTakenMs = Date.now() - this.startTime;
    
    // Process the round
    const processedRound = processRound(
      currentRound.panoramaLocation,
      guessLocation,
      currentRound.roundNumber,
      timeTakenMs
    );

    // Update the round in the game session
    const roundIndex = this.gameSession.rounds.length - 1;
    this.gameSession.rounds[roundIndex] = processedRound;

    // Update total score
    this.gameSession.totalScore = calculateTotalScore(this.gameSession.rounds);

    // Check if game is completed
    if (this.gameSession.rounds.length === GAME_CONFIG.TOTAL_ROUNDS) {
      this.completeGame();
    }

    return processedRound;
  }

  /**
   * Complete the game
   */
  private completeGame(): void {
    this.gameSession.isCompleted = true;
    this.gameSession.endTime = new Date();
  }

  /**
   * Get the current active round
   * @returns Current round or null if no active round
   */
  getCurrentRound(): GameRound | null {
    if (this.gameSession.rounds.length === 0) {
      return null;
    }

    const lastRound = this.gameSession.rounds[this.gameSession.rounds.length - 1];
    
    // If the last round doesn't have a guess, it's the current round
    if (!lastRound.guessLocation) {
      return lastRound;
    }

    return null;
  }

  /**
   * Get the current round number
   * @returns Current round number (1-based)
   */
  getCurrentRoundNumber(): number {
    return this.gameSession.rounds.length;
  }

  /**
   * Check if the game is completed
   * @returns True if game is completed
   */
  isGameCompleted(): boolean {
    return this.gameSession.isCompleted;
  }

  /**
   * Get the game session data
   * @returns Current game session
   */
  getGameSession(): GameSession {
    return { ...this.gameSession };
  }

  /**
   * Get game statistics
   * @returns Game statistics
   */
  getGameStats() {
    const completedRounds = this.gameSession.rounds.filter(round => round.score !== null);
    const totalDistance = completedRounds.reduce((sum, round) => sum + (round.distanceKm || 0), 0);
    const averageDistance = completedRounds.length > 0 ? totalDistance / completedRounds.length : 0;
    const bestRound = completedRounds.reduce((best, round) => 
      (round.score || 0) > (best.score || 0) ? round : best, completedRounds[0]);

    return {
      totalScore: this.gameSession.totalScore,
      completedRounds: completedRounds.length,
      totalRounds: GAME_CONFIG.TOTAL_ROUNDS,
      averageDistance: Math.round(averageDistance * 100) / 100,
      bestRound,
      isCompleted: this.gameSession.isCompleted,
    };
  }
}
