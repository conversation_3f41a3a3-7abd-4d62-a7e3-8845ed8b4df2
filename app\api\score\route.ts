import { NextRequest, NextResponse } from 'next/server';
import { calculateDistance, calculateScore } from '@/lib/gameLogic';
import { Location } from '@/types/game';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { panoramaLocation, guessLocation }: {
      panoramaLocation: Location;
      guessLocation: Location;
    } = body;
    
    // Validate input
    if (!panoramaLocation || !guessLocation) {
      return NextResponse.json(
        {
          success: false,
          message: 'Both panoramaLocation and guessLocation are required'
        },
        { status: 400 }
      );
    }
    
    // Validate coordinates
    if (
      typeof panoramaLocation.lat !== 'number' ||
      typeof panoramaLocation.lng !== 'number' ||
      typeof guessLocation.lat !== 'number' ||
      typeof guessLocation.lng !== 'number'
    ) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid coordinates provided'
        },
        { status: 400 }
      );
    }
    
    // Calculate distance and score
    const distanceKm = calculateDistance(panoramaLocation, guessLocation);
    const score = calculateScore(distanceKm);
    
    return NextResponse.json({
      success: true,
      data: {
        distanceKm,
        score,
        panoramaLocation,
        guessLocation
      }
    });
  } catch (error) {
    console.error('Error calculating score:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to calculate score',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
