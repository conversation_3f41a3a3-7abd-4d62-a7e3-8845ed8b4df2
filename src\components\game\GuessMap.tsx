'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Location } from '@/types/game';
import { initializeLeaflet } from '@/lib/openStreetMap';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

interface GuessMapProps {
  onGuessChange: (location: Location | null) => void;
  guessLocation: Location | null;
  actualLocation?: Location;
  showActualLocation?: boolean;
  className?: string;
}

export default function GuessMap({ 
  onGuessChange, 
  guessLocation, 
  actualLocation, 
  showActualLocation = false,
  className = '' 
}: GuessMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const mapInstance = useRef<L.Map | null>(null);
  const guessMarker = useRef<L.Marker | null>(null);
  const actualMarker = useRef<L.Marker | null>(null);
  const distanceLine = useRef<L.Polyline | null>(null);

  useEffect(() => {
    initializeMap();
  }, []);

  useEffect(() => {
    updateGuessMarker();
  }, [guessLocation]);

  useEffect(() => {
    updateActualLocationDisplay();
  }, [actualLocation, showActualLocation]);

  const initializeMap = async () => {
    if (!mapRef.current) return;

    setIsLoading(true);
    setError(null);

    try {
      // Initialize Leaflet
      await initializeLeaflet();

      // Create map
      const map = L.map(mapRef.current, {
        center: [20, 0], // Center on world
        zoom: 2,
        zoomControl: true,
        attributionControl: true,
      });

      // Add OpenStreetMap tile layer
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 19,
      }).addTo(map);

      mapInstance.current = map;

      // Add click listener for placing guess
      map.on('click', (event: L.LeafletMouseEvent) => {
        if (!showActualLocation) {
          const location: Location = {
            lat: event.latlng.lat,
            lng: event.latlng.lng,
          };
          onGuessChange(location);
        }
      });

      setIsLoading(false);
    } catch (err) {
      console.error('Error initializing map:', err);
      setError(err instanceof Error ? err.message : 'Failed to load map');
      setIsLoading(false);
    }
  };

  const updateGuessMarker = () => {
    if (!mapInstance.current) return;

    // Remove existing guess marker
    if (guessMarker.current) {
      mapInstance.current.removeLayer(guessMarker.current);
      guessMarker.current = null;
    }

    // Add new guess marker if location exists
    if (guessLocation) {
      // Create custom icon for guess marker
      const guessIcon = L.divIcon({
        html: `<div style="background-color: #3B82F6; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
        className: 'custom-marker',
        iconSize: [20, 20],
        iconAnchor: [10, 10],
      });

      guessMarker.current = L.marker([guessLocation.lat, guessLocation.lng], {
        icon: guessIcon,
        draggable: !showActualLocation,
      }).addTo(mapInstance.current);

      // Add drag listener if marker is draggable
      if (!showActualLocation) {
        guessMarker.current.on('dragend', (event: L.DragEndEvent) => {
          const marker = event.target;
          const position = marker.getLatLng();
          const location: Location = {
            lat: position.lat,
            lng: position.lng,
          };
          onGuessChange(location);
        });
      }
    }
  };

  const updateActualLocationDisplay = () => {
    if (!mapInstance.current) return;

    // Remove existing actual marker and line
    if (actualMarker.current) {
      mapInstance.current.removeLayer(actualMarker.current);
      actualMarker.current = null;
    }
    if (distanceLine.current) {
      mapInstance.current.removeLayer(distanceLine.current);
      distanceLine.current = null;
    }

    // Add actual location marker if should be shown
    if (showActualLocation && actualLocation) {
      // Create custom icon for actual location marker
      const actualIcon = L.divIcon({
        html: `<div style="background-color: #EF4444; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
        className: 'custom-marker',
        iconSize: [20, 20],
        iconAnchor: [10, 10],
      });

      actualMarker.current = L.marker([actualLocation.lat, actualLocation.lng], {
        icon: actualIcon,
      }).addTo(mapInstance.current);

      // Draw line between guess and actual location
      if (guessLocation) {
        distanceLine.current = L.polyline([
          [guessLocation.lat, guessLocation.lng],
          [actualLocation.lat, actualLocation.lng]
        ], {
          color: '#EF4444',
          weight: 2,
          opacity: 1.0,
        }).addTo(mapInstance.current);

        // Fit map to show both markers
        const group = L.featureGroup([guessMarker.current!, actualMarker.current]);
        mapInstance.current.fitBounds(group.getBounds(), { padding: [20, 20] });
      }
    }
  };

  const clearGuess = () => {
    onGuessChange(null);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600">Loading Map...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center">
            <p className="text-red-600 mb-2">{error}</p>
            <button 
              onClick={initializeMap}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      )}

      {/* Map Container */}
      <div 
        ref={mapRef} 
        className="w-full h-full min-h-[400px] rounded-lg overflow-hidden"
      />

      {/* Controls */}
      {!isLoading && !error && !showActualLocation && (
        <div className="absolute top-4 right-4 z-10 space-y-2">
          {guessLocation && (
            <button
              onClick={clearGuess}
              className="block px-3 py-2 bg-white bg-opacity-90 text-gray-700 rounded shadow hover:bg-opacity-100 transition-all text-sm"
              title="Clear Guess"
            >
              🗑️ Clear
            </button>
          )}
          <div className="px-3 py-2 bg-white bg-opacity-90 text-gray-700 rounded shadow text-xs">
            Click to place your guess
          </div>
        </div>
      )}
    </div>
  );
}
