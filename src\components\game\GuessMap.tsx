'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Location } from '@/types/game';
import { loadGoogleMapsAPI } from '@/lib/googleMaps';

interface GuessMapProps {
  onGuessChange: (location: Location | null) => void;
  guessLocation: Location | null;
  actualLocation?: Location;
  showActualLocation?: boolean;
  className?: string;
}

export default function GuessMap({ 
  onGuessChange, 
  guessLocation, 
  actualLocation, 
  showActualLocation = false,
  className = '' 
}: GuessMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const mapInstance = useRef<google.maps.Map | null>(null);
  const guessMarker = useRef<google.maps.Marker | null>(null);
  const actualMarker = useRef<google.maps.Marker | null>(null);
  const distanceLine = useRef<google.maps.Polyline | null>(null);

  useEffect(() => {
    initializeMap();
  }, []);

  useEffect(() => {
    updateGuessMarker();
  }, [guessLocation]);

  useEffect(() => {
    updateActualLocationDisplay();
  }, [actualLocation, showActualLocation]);

  const initializeMap = async () => {
    if (!mapRef.current) return;

    setIsLoading(true);
    setError(null);

    try {
      // Load Google Maps API
      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
      if (!apiKey) {
        throw new Error('Google Maps API key not found');
      }

      await loadGoogleMapsAPI(apiKey);

      // Create map
      const map = new google.maps.Map(mapRef.current, {
        zoom: 2,
        center: { lat: 20, lng: 0 }, // Center on world
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        streetViewControl: false,
        mapTypeControl: true,
        fullscreenControl: false,
        zoomControl: true,
        scaleControl: true,
      });

      mapInstance.current = map;

      // Add click listener for placing guess
      map.addListener('click', (event: google.maps.MapMouseEvent) => {
        if (event.latLng && !showActualLocation) {
          const location: Location = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng(),
          };
          onGuessChange(location);
        }
      });

      setIsLoading(false);
    } catch (err) {
      console.error('Error initializing map:', err);
      setError(err instanceof Error ? err.message : 'Failed to load map');
      setIsLoading(false);
    }
  };

  const updateGuessMarker = () => {
    if (!mapInstance.current) return;

    // Remove existing guess marker
    if (guessMarker.current) {
      guessMarker.current.setMap(null);
    }

    // Add new guess marker if location exists
    if (guessLocation) {
      guessMarker.current = new google.maps.Marker({
        position: guessLocation,
        map: mapInstance.current,
        title: 'Your Guess',
        icon: {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#3B82F6"/>
              <circle cx="12" cy="9" r="2.5" fill="white"/>
            </svg>
          `),
          scaledSize: new google.maps.Size(24, 24),
          anchor: new google.maps.Point(12, 24),
        },
        draggable: !showActualLocation,
      });

      // Add drag listener if marker is draggable
      if (!showActualLocation) {
        guessMarker.current.addListener('dragend', (event: google.maps.MapMouseEvent) => {
          if (event.latLng) {
            const location: Location = {
              lat: event.latLng.lat(),
              lng: event.latLng.lng(),
            };
            onGuessChange(location);
          }
        });
      }
    }
  };

  const updateActualLocationDisplay = () => {
    if (!mapInstance.current) return;

    // Remove existing actual marker and line
    if (actualMarker.current) {
      actualMarker.current.setMap(null);
    }
    if (distanceLine.current) {
      distanceLine.current.setMap(null);
    }

    // Add actual location marker if should be shown
    if (showActualLocation && actualLocation) {
      actualMarker.current = new google.maps.Marker({
        position: actualLocation,
        map: mapInstance.current,
        title: 'Actual Location',
        icon: {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#EF4444"/>
              <circle cx="12" cy="9" r="2.5" fill="white"/>
            </svg>
          `),
          scaledSize: new google.maps.Size(24, 24),
          anchor: new google.maps.Point(12, 24),
        },
      });

      // Draw line between guess and actual location
      if (guessLocation) {
        distanceLine.current = new google.maps.Polyline({
          path: [guessLocation, actualLocation],
          geodesic: true,
          strokeColor: '#EF4444',
          strokeOpacity: 1.0,
          strokeWeight: 2,
          map: mapInstance.current,
        });

        // Fit map to show both markers
        const bounds = new google.maps.LatLngBounds();
        bounds.extend(guessLocation);
        bounds.extend(actualLocation);
        mapInstance.current.fitBounds(bounds);
      }
    }
  };

  const clearGuess = () => {
    onGuessChange(null);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600">Loading Map...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center">
            <p className="text-red-600 mb-2">{error}</p>
            <button 
              onClick={initializeMap}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      )}

      {/* Map Container */}
      <div 
        ref={mapRef} 
        className="w-full h-full min-h-[400px] rounded-lg overflow-hidden"
      />

      {/* Controls */}
      {!isLoading && !error && !showActualLocation && (
        <div className="absolute top-4 right-4 z-10 space-y-2">
          {guessLocation && (
            <button
              onClick={clearGuess}
              className="block px-3 py-2 bg-white bg-opacity-90 text-gray-700 rounded shadow hover:bg-opacity-100 transition-all text-sm"
              title="Clear Guess"
            >
              🗑️ Clear
            </button>
          )}
          <div className="px-3 py-2 bg-white bg-opacity-90 text-gray-700 rounded shadow text-xs">
            Click to place your guess
          </div>
        </div>
      )}
    </div>
  );
}
