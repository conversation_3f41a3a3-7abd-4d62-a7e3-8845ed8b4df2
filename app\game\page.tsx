'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Category, Location, GameRound } from '@/types/game';
import { GAME_CONFIG } from '@/utils/constants';
import StreetViewPanorama from '@/components/game/StreetViewPanorama';
import GuessMap from '@/components/game/GuessMap';
import RoundInfo from '@/components/game/RoundInfo';
import GameControls from '@/components/game/GameControls';
import ScoreDisplay from '@/components/game/ScoreDisplay';

type GamePhase = 'loading' | 'guessing' | 'result' | 'complete';

export default function GamePage() {
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [gamePhase, setGamePhase] = useState<GamePhase>('loading');
  const [currentRound, setCurrentRound] = useState(1);
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [guessLocation, setGuessLocation] = useState<Location | null>(null);
  const [rounds, setRounds] = useState<GameRound[]>([]);
  const [totalScore, setTotalScore] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  useEffect(() => {
    initializeGame();
  }, []);

  const initializeGame = () => {
    // Get selected category from localStorage
    const storedCategory = localStorage.getItem('selectedCategory');
    if (storedCategory) {
      setSelectedCategory(JSON.parse(storedCategory));
      startNewRound();
    } else {
      // If no category selected, redirect to home
      router.push('/');
    }
  };

  const startNewRound = async () => {
    setGamePhase('loading');
    setGuessLocation(null);

    try {
      // Fetch a random location for this round
      const response = await fetch(`/api/locations?categoryId=${selectedCategory?._id}`);
      const data = await response.json();

      if (data.success) {
        setCurrentLocation(data.data.location);
        setGamePhase('guessing');
      } else {
        console.error('Failed to fetch location:', data.message);
        // Fallback to a default location
        setCurrentLocation({ lat: 40.7128, lng: -74.0060 }); // New York
        setGamePhase('guessing');
      }
    } catch (error) {
      console.error('Error fetching location:', error);
      // Fallback to a default location
      setCurrentLocation({ lat: 40.7128, lng: -74.0060 }); // New York
      setGamePhase('guessing');
    }
  };

  const handleSubmitGuess = async () => {
    if (!guessLocation || !currentLocation) return;

    setIsSubmitting(true);

    try {
      // Calculate score
      const response = await fetch('/api/score', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          panoramaLocation: currentLocation,
          guessLocation: guessLocation,
        }),
      });

      const data = await response.json();

      if (data.success) {
        const newRound: GameRound = {
          roundNumber: currentRound,
          panoramaLocation: currentLocation,
          guessLocation: guessLocation,
          distanceKm: data.data.distanceKm,
          score: data.data.score,
          timeTakenMs: Date.now(), // Simplified for now
        };

        setRounds(prev => [...prev, newRound]);
        setTotalScore(prev => prev + data.data.score);
        setGamePhase('result');
      } else {
        console.error('Failed to calculate score:', data.message);
      }
    } catch (error) {
      console.error('Error submitting guess:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNextRound = () => {
    if (currentRound >= GAME_CONFIG.TOTAL_ROUNDS) {
      setGamePhase('complete');
    } else {
      setCurrentRound(prev => prev + 1);
      startNewRound();
    }
  };

  const handleQuitGame = () => {
    router.push('/');
  };

  const getCurrentRoundData = (): GameRound | null => {
    return rounds.find(round => round.roundNumber === currentRound) || null;
  };

  // Loading state
  if (!selectedCategory) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading game...</p>
        </div>
      </div>
    );
  }

  // Game complete state
  if (gamePhase === 'complete') {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">Game Complete!</h1>
          <p className="text-xl text-gray-600 mb-8">
            Final Score: {totalScore} points
          </p>
          <button
            onClick={() => router.push('/results')}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mr-4"
          >
            View Results
          </button>
          <button
            onClick={() => router.push('/')}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Play Again
          </button>
        </div>
      </div>
    );
  }

  const currentRoundData = getCurrentRoundData();

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <RoundInfo
            currentRound={currentRound}
            totalScore={totalScore}
            roundScore={currentRoundData?.score}
          />
        </div>
      </div>

      {/* Main Game Area */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-[calc(100vh-200px)]">
          {/* Street View */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="p-4 border-b">
              <h2 className="text-lg font-semibold text-gray-800">Where are you?</h2>
            </div>
            <div className="h-full">
              {currentLocation && gamePhase !== 'loading' && (
                <StreetViewPanorama
                  location={currentLocation}
                  className="h-full"
                />
              )}
              {gamePhase === 'loading' && (
                <div className="h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                    <p className="text-gray-600">Loading location...</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Map and Results */}
          <div className="space-y-6">
            {/* Guess Map */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden flex-1">
              <div className="p-4 border-b">
                <h2 className="text-lg font-semibold text-gray-800">Make your guess</h2>
              </div>
              <div className="h-96">
                <GuessMap
                  onGuessChange={setGuessLocation}
                  guessLocation={guessLocation}
                  actualLocation={gamePhase === 'result' ? currentLocation : undefined}
                  showActualLocation={gamePhase === 'result'}
                  className="h-full"
                />
              </div>
            </div>

            {/* Score Display (only show during result phase) */}
            {gamePhase === 'result' && currentRoundData && (
              <ScoreDisplay
                distance={currentRoundData.distanceKm || 0}
                score={currentRoundData.score || 0}
                maxScore={GAME_CONFIG.MAX_SCORE_PER_ROUND}
              />
            )}
          </div>
        </div>

        {/* Game Controls */}
        <div className="mt-6">
          <GameControls
            guessLocation={guessLocation}
            onSubmitGuess={handleSubmitGuess}
            onNextRound={handleNextRound}
            onQuitGame={handleQuitGame}
            showSubmit={gamePhase === 'guessing'}
            showNext={gamePhase === 'result'}
            isSubmitting={isSubmitting}
          />
        </div>
      </div>
    </div>
  );
}
