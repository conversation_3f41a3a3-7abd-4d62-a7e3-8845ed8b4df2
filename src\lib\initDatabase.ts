import connectToDatabase from './mongo';
import Category from './models/Category';
import { DEFAULT_CATEGORIES } from '@/utils/constants';

/**
 * Initialize the database with default data
 */
export async function initializeDatabase(): Promise<void> {
  try {
    await connectToDatabase();
    console.log('Connected to database');
    
    // Initialize categories if they don't exist
    await initializeCategories();
    
    console.log('Database initialization completed');
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
}

/**
 * Initialize default categories in the database
 */
async function initializeCategories(): Promise<void> {
  try {
    const existingCategories = await Category.find();
    
    if (existingCategories.length === 0) {
      console.log('Initializing default categories...');
      
      const categoriesToInsert = DEFAULT_CATEGORIES.map(cat => ({
        _id: cat._id,
        name: cat.name,
        description: cat.description,
        queryConfig: cat.queryConfig,
        isActive: cat.isActive,
      }));
      
      await Category.insertMany(categoriesToInsert);
      console.log(`Inserted ${categoriesToInsert.length} default categories`);
    } else {
      console.log(`Found ${existingCategories.length} existing categories`);
    }
  } catch (error) {
    console.error('Failed to initialize categories:', error);
    throw error;
  }
}
