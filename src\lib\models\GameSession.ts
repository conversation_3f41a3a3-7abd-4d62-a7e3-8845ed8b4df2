import mongoose, { Schema, Document } from 'mongoose';
import { GameSession, GameRound, Location } from '@/types/game';

export interface GameSessionDocument extends Omit<GameSession, '_id'>, Document {}

const LocationSchema = new Schema<Location>({
  lat: {
    type: Number,
    required: true,
  },
  lng: {
    type: Number,
    required: true,
  },
}, { _id: false });

const GameRoundSchema = new Schema<GameRound>({
  roundNumber: {
    type: Number,
    required: true,
    min: 1,
    max: 5,
  },
  panoramaLocation: {
    type: LocationSchema,
    required: true,
  },
  guessLocation: {
    type: LocationSchema,
    default: null,
  },
  distanceKm: {
    type: Number,
    default: null,
  },
  score: {
    type: Number,
    default: null,
  },
  timeTakenMs: {
    type: Number,
    default: null,
  },
}, { _id: false });

const GameSessionSchema = new Schema<GameSessionDocument>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    default: null,
  },
  categoryId: {
    type: String,
    required: true,
  },
  startTime: {
    type: Date,
    default: Date.now,
  },
  endTime: {
    type: Date,
    default: null,
  },
  totalScore: {
    type: Number,
    default: 0,
  },
  isCompleted: {
    type: Boolean,
    default: false,
  },
  rounds: {
    type: [GameRoundSchema],
    default: [],
  },
}, {
  timestamps: true,
});

// Create indexes
GameSessionSchema.index({ userId: 1 });
GameSessionSchema.index({ startTime: -1 });
GameSessionSchema.index({ isCompleted: 1 });
GameSessionSchema.index({ categoryId: 1 });

export default mongoose.models.GameSession || mongoose.model<GameSessionDocument>('GameSession', GameSessionSchema);
