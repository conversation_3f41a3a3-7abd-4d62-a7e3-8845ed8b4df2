import { Location } from '@/types/game';
import L from 'leaflet';

// Extend the Window interface to include Leaflet
declare global {
  interface Window {
    L: typeof L;
  }
}

/**
 * Initialize Leaflet (no API key needed for OpenStreetMap)
 * @returns Promise that resolves when Leaflet is ready
 */
export function initializeLeaflet(): Promise<void> {
  return new Promise((resolve) => {
    // Leaflet is already loaded via import, just resolve
    window.L = L;
    resolve();
  });
}

/**
 * Generate a random location for Street View (placeholder implementation)
 * In a real implementation, this would use a curated list or Google's API
 * @returns Random location
 */
export function generateRandomStreetViewLocation(): Location {
  // This is a simplified implementation
  // In production, you'd want to use a curated list of known Street View locations
  const locations: Location[] = [
    { lat: 40.7128, lng: -74.0060 }, // New York
    { lat: 51.5074, lng: -0.1278 },  // London
    { lat: 48.8566, lng: 2.3522 },   // Paris
    { lat: 35.6762, lng: 139.6503 }, // Tokyo
    { lat: -33.8688, lng: 151.2093 }, // Sydney
    { lat: 37.7749, lng: -122.4194 }, // San Francisco
    { lat: 55.7558, lng: 37.6176 },  // Moscow
    { lat: -22.9068, lng: -43.1729 }, // Rio de Janeiro
  ];

  return locations[Math.floor(Math.random() * locations.length)];
}

/**
 * Calculate distance using Haversine formula (no external API needed)
 * @param point1 First location
 * @param point2 Second location
 * @returns Distance in kilometers
 */
export function calculateDistance(point1: Location, point2: Location): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(point2.lat - point1.lat);
  const dLng = toRadians(point2.lng - point1.lng);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(point1.lat)) * Math.cos(toRadians(point2.lat)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;

  return Math.round(distance * 100) / 100; // Round to 2 decimal places
}

/**
 * Convert degrees to radians
 * @param degrees Degrees to convert
 * @returns Radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}
