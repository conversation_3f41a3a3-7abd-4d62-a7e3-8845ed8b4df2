import React from 'react';
import { formatScore } from '@/utils/helpers';
import { GAME_CONFIG } from '@/utils/constants';

interface RoundInfoProps {
  currentRound: number;
  totalScore: number;
  roundScore?: number;
  className?: string;
}

export default function RoundInfo({ 
  currentRound, 
  totalScore, 
  roundScore,
  className = '' 
}: RoundInfoProps) {
  return (
    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
      <div className="flex items-center justify-between">
        {/* Round Progress */}
        <div className="flex items-center space-x-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-800">
              Round {currentRound}
            </div>
            <div className="text-sm text-gray-600">
              of {GAME_CONFIG.TOTAL_ROUNDS}
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="w-32 bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ 
                width: `${(currentRound / GAME_CONFIG.TOTAL_ROUNDS) * 100}%` 
              }}
            />
          </div>
        </div>

        {/* Scores */}
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-800">
            {formatScore(totalScore)}
          </div>
          <div className="text-sm text-gray-600">
            Total Score
          </div>
          {roundScore !== undefined && (
            <div className="text-lg font-semibold text-blue-600 mt-1">
              +{formatScore(roundScore)} this round
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
