// Application-wide constants for Geo-Gusser

export const GAME_CONFIG = {
  TOTAL_ROUNDS: 5,
  MAX_SCORE_PER_ROUND: 5000,
  MIN_SCORE_PER_ROUND: 0,
} as const;

export const SCORING = {
  // Distance thresholds in kilometers
  PERFECT_DISTANCE: 1, // Within 1km = max score
  GOOD_DISTANCE: 25,   // Within 25km = good score
  FAIR_DISTANCE: 250,  // Within 250km = fair score
  POOR_DISTANCE: 2500, // Within 2500km = poor score
  // Beyond 2500km = minimum score
} as const;

export const API_ENDPOINTS = {
  LOCATIONS: '/api/locations',
  SCORE: '/api/score',
  CATEGORIES: '/api/categories',
} as const;

export const DEFAULT_CATEGORIES = [
  {
    _id: 'world-standard',
    name: 'World Standard',
    description: 'Random locations from around the world',
    queryConfig: { area: 'global' },
    isActive: true,
  },
  {
    _id: 'cities',
    name: 'World Cities',
    description: 'Major cities around the world',
    queryConfig: { area: 'cities' },
    isActive: true,
  },
  {
    _id: 'rural',
    name: 'Rural Areas',
    description: 'Countryside and rural locations',
    queryConfig: { area: 'rural' },
    isActive: true,
  },
] as const;
