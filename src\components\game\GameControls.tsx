import React from 'react';
import Button from '@/components/ui/Button';
import { Location } from '@/types/game';

interface GameControlsProps {
  guessLocation: Location | null;
  onSubmitGuess: () => void;
  onNextRound: () => void;
  onQuitGame: () => void;
  showSubmit: boolean;
  showNext: boolean;
  isSubmitting?: boolean;
  className?: string;
}

export default function GameControls({
  guessLocation,
  onSubmitGuess,
  onNextRound,
  onQuitGame,
  showSubmit,
  showNext,
  isSubmitting = false,
  className = ''
}: GameControlsProps) {
  return (
    <div className={`bg-white rounded-lg shadow-md p-4 ${className}`}>
      <div className="flex items-center justify-between">
        {/* Left side - Quit button */}
        <Button
          variant="outline"
          onClick={onQuitGame}
          className="text-red-600 border-red-600 hover:bg-red-600 hover:text-white"
        >
          Quit Game
        </Button>

        {/* Right side - Main action buttons */}
        <div className="flex items-center space-x-4">
          {showSubmit && (
            <Button
              onClick={onSubmitGuess}
              disabled={!guessLocation || isSubmitting}
              size="lg"
              className="px-8"
            >
              {isSubmitting ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Submitting...</span>
                </div>
              ) : (
                'Submit Guess'
              )}
            </Button>
          )}

          {showNext && (
            <Button
              onClick={onNextRound}
              size="lg"
              className="px-8"
            >
              Next Round
            </Button>
          )}
        </div>
      </div>

      {/* Hint text */}
      {showSubmit && !guessLocation && (
        <div className="mt-3 text-center text-sm text-gray-600">
          Place a marker on the map to make your guess
        </div>
      )}
    </div>
  );
}
