// Core game types for Geo-Gusser

export interface Location {
  lat: number;
  lng: number;
}

export interface GameRound {
  roundNumber: number;
  panoramaLocation: Location;
  guessLocation: Location | null;
  distanceKm: number | null;
  score: number | null;
  timeTakenMs: number | null;
}

export interface GameSession {
  _id?: string;
  userId?: string;
  categoryId: string;
  startTime: Date;
  endTime?: Date;
  totalScore: number;
  isCompleted: boolean;
  rounds: GameRound[];
}

export interface Category {
  _id: string;
  name: string;
  description: string;
  queryConfig: Record<string, unknown>;
  isActive: boolean;
}

export interface User {
  _id: string;
  username?: string;
  email: string;
  passwordHash: string;
  createdAt: Date;
  updatedAt: Date;
  gamesPlayed: number;
  totalScore: number;
  bestScore: number;
}

export interface GameState {
  currentRound: number;
  rounds: GameRound[];
  totalScore: number;
  isGameActive: boolean;
  selectedCategory: Category | null;
  currentPanoramaLocation: Location | null;
  userGuess: Location | null;
}

export type GameStatus = 'setup' | 'playing' | 'round-complete' | 'game-complete';
