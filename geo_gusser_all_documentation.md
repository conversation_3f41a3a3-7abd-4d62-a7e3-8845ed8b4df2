# Geo-Gusser

## Project Description
A dynamic web-based location-guessing game, similar to Geoguesser, built with Next.js. Key features include displaying a random Street View panorama, allowing users to pinpoint their guess on an interactive map, calculating the distance and score for each round, and displaying a summary of results.

## Product Requirements Document
Product Requirements Document (PRD) - Geo-Gusser

1.0 Introduction



1.1 Purpose

This document outlines the product requirements for "Geo-Gusser", a dynamic web-based location-guessing game. It serves as a comprehensive guide for the development team, detailing the goals, features, functionalities, and technical specifications for the initial release (V1) of the application.



1.2 Scope

The scope of this document covers the development of a single-player, web-based game accessible via modern web browsers. It includes the core game loop, user interface, scoring mechanism, and integration with external APIs. Multiplayer functionality is explicitly out of scope for V1.



1.3 Vision & Goals

The vision for Geo-Gusser is to provide an engaging, visually appealing, and intuitive platform for users to test their geographical knowledge. The primary goals are:

- To deliver a fun and challenging single-player game experience.

- To provide a seamless and interactive user interface.

- To accurately calculate and display scores based on guess proximity.

- To support specific location categories for diverse gameplay.



2.0 Target Audience



Geo-Gusser is designed for a general audience, accessible and enjoyable for anyone interested in casual web-based games and geography. No specific demographic or technical proficiency is required to play the game.



3.0 User Flow & Interactions



The primary user flow for Geo-<PERSON>ser is as follows:

1.  **Landing Page Access:** A user navigates to the Geo-Gusser web application.

2.  **Landing Page View:** The user is presented with a "Beautiful Landing page" featuring a clear "Play Now" button.

3.  **Game Initiation:** The user clicks the "Play Now" button, initiating a new game session.

4.  **Optional Account Creation/Login:** Prior to or during gameplay, users will have the *optional* ability to create an account or log in. Gameplay does not require an account.

5.  **Game Page Display:** The user is directed to the main game page. This page will display a random Street View panorama of a location and an interactive map.

6.  **Guessing Phase:** The user analyzes the Street View and pinpoints their guessed location on the interactive map.

7.  **Guess Submission:** The user submits their guess.

8.  **Round Scoring:** The system calculates the distance between the guessed location and the actual location, then assigns a score for the round. This score is displayed to the user.

9.  **Next Round:** The game proceeds to the next round. This cycle repeats for a total of 5 rounds.

10. **Results Summary:** After the completion of 5 rounds, a comprehensive score board is displayed, summarizing the results of the game.



4.0 Features & Functionality



4.1 Core Game Mechanics

-   **Random Street View Panorama Display:** The game will present a random Street View panorama from a diverse set of global locations for each round.

-   **Interactive Guessing Map:** An interactive map will be provided for users to place their guess. This map will allow for zooming and panning.

-   **Location Pinpointing:** Users must be able to accurately place a marker (pin) on the interactive map to indicate their guessed location.

-   **Round-Based Gameplay:** Each game session will consist of 5 distinct rounds.



4.2 Landing Page

-   **Visual Appeal:** The landing page will adhere to the "Minimal and Beautiful also unique" UI/UX preference.

-   **Play Now Button:** A prominent and intuitive button to start a new game.

-   **Optional Login/Signup:** Clear pathways for optional user account creation or login, without hindering immediate gameplay.



4.3 Game Page

-   **Layout:** A well-structured layout displaying both the Street View panorama and the interactive map simultaneously (e.g., split-screen).

-   **Street View Integration:** Seamless integration of Google Maps Street View API for panorama display and navigation within the panorama.

-   **Map Integration:** Seamless integration of Google Maps JavaScript API for the interactive guessing map, including controls for zooming, panning, and placing markers.

-   **Round Indicator:** A clear display of the current round number (e.g., "Round 3 of 5").

-   **Submit Guess Button:** A clear call to action button to submit the user's guess for the current round.



4.4 Scoring System

-   **Distance-Based Approach:** Scoring will be solely based on the distance between the user's guessed location and the actual location of the Street View panorama.

-   **Score Calculation:** The closer the guess to the actual location, the higher the score. A specific formula will be implemented to translate distance into points, ensuring a reasonable distribution (e.g., maximum points for very close guesses, diminishing returns as distance increases, minimum points for very far guesses).

-   **Round Score Display:** After each guess, the distance and score for that specific round will be displayed.



4.5 Results Summary

-   **Total Score Display:** Upon completion of all 5 rounds, a final cumulative score will be presented.

-   **Round-by-Round Breakdown:** A detailed summary for each of the 5 rounds, including:

    -   Guessed Location (coordinates or approximate address).

    -   Actual Location (coordinates or approximate address).

    -   Distance between guess and actual.

    -   Score obtained for that round.

-   **Play Again Option:** A button or link to easily start a new game.



4.6 Location Categories

-   **Category Selection:** Users will be able to select from predefined location categories before starting a game.

-   **Initial Categories:** Examples of categories include, but are not limited to: "World (Random)", "Cities", "Landmarks", "Rural Areas", or specific countries (e.g., "USA", "France").

-   **Dynamic Location Loading:** The selected category will influence the random Street View panorama chosen for each round.



5.0 Technical Specifications (High-Level)



5.1 Technology Stack

-   **Frontend & Backend:** Next.js (utilizing its React framework for UI and API routes for server-side logic).

-   **Database:** MongoDB for persistent data storage (e.g., user profiles if accounts are implemented, potentially curated location data for categories).



5.2 API Integrations

-   **Google Maps Platform:**

    -   Google Maps Street View API: For displaying panoramic imagery.

    -   Google Maps JavaScript API: For rendering the interactive map, handling user pin placement, and potentially calculating distances.

    -   (Potentially) Google Geocoding API: For converting coordinates to human-readable addresses for results display, if needed.



6.0 Non-Functional Requirements



6.1 Performance

-   **Load Times:** Optimized loading times for the application, especially for Street View panoramas and map tiles.

-   **Responsiveness:** The application must be fully responsive, providing a consistent and smooth experience across various devices (desktop, tablet, mobile) and screen sizes.

-   **API Efficiency:** Efficient handling of API requests to Google Maps to minimize latency and ensure a fluid user experience.



6.2 Scalability

-   **Current Scope:** V1 focuses solely on a single-player experience, therefore, no specific multiplayer scalability concerns are present for this iteration.

-   **Future-Proofing:** The architecture should be designed to allow for future enhancements (e.g., user profiles, saved scores, more complex game modes) without requiring major re-architecture. MongoDB's scalability characteristics align with potential future growth.



6.3 Usability & UX

-   **Intuitive Interface:** The UI should be highly intuitive, allowing users of all levels to easily understand and play the game.

-   **Visual Design:** Adherence to the specified "Minimal and Beautiful also unique" design aesthetic.

-   **Clear Feedback:** Provide clear and immediate feedback to the user on actions (e.g., guess submitted, score updated, round completion).



6.4 Security

-   **API Key Management:** Google Maps API keys must be securely managed and not exposed client-side. Server-side proxying or environment variable usage is required.

-   **Data Protection:** Basic data protection for any user data stored in MongoDB (if accounts are implemented) through appropriate security measures.



7.0 Constraints & Dependencies



7.1 Timeline

-   **Development Period:** The project is constrained by a strict timeline of 4 weeks for completion of the V1 application. This necessitates careful prioritization and scope management.



7.2 Dependencies

-   **Google Maps Platform:** The core functionality of Geo-Gusser is entirely dependent on the availability and performance of Google Maps Platform APIs. Any changes or limitations imposed by Google could impact the application.

-   **API Quotas/Costs:** Awareness and management of Google Maps API usage quotas and associated costs are critical.



8.0 Future Enhancements



No immediate future enhancements are planned beyond the V1 scope as defined in this document. Any future features or game modes will be documented in a subsequent PRD version.

## Technology Stack
TECHNOLOGY STACK DOCUMENT: Geo-Gusser

This document outlines the recommended technology stack for the "Geo-Gusser" project, a dynamic web-based location-guessing game. The selections prioritize rapid development, performance, and alignment with the project's core requirements, design preferences, and the 4-week timeline.

1.  FRONTEND TECHNOLOGIES

    1.1.  Next.js
        *   Recommendation: Next.js is the foundational framework for the frontend.
        *   Justification: Explicitly requested in the project description and ideal for modern web applications. Its capabilities for Server-Side Rendering (SSR) or Static Site Generation (SSG) provide performance benefits, faster initial page loads, and improved SEO (though less critical for a game, it's a good practice). The built-in file-system routing simplifies page creation and navigation. Next.js also natively supports API Routes, allowing for a unified full-stack development approach, which is crucial for the tight 4-week timeline by avoiding the overhead of a separate backend server setup.

    1.2.  React
        *   Recommendation: The core UI library, inherently part of Next.js.
        *   Justification: Provides a component-based architecture for building interactive and complex user interfaces efficiently. Its declarative nature simplifies UI development, which is beneficial for the game's interactive map and score display.

    1.3.  TypeScript
        *   Recommendation: Strongly recommended for all frontend and backend (API routes) development.
        *   Justification: Introduces static typing, which significantly improves code quality, reduces runtime errors, and enhances developer productivity through better autocompletion and refactoring support. Given the integration with external APIs (Google Maps) and a database (MongoDB), type safety ensures data consistency and reduces integration bugs.

    1.4.  Styling Solution: Tailwind CSS
        *   Recommendation: Utilize Tailwind CSS for all styling needs.
        *   Justification: Aligns perfectly with the "Minimal and Beautiful, also unique" UI design preference. Its utility-first approach enables rapid UI development and prototyping, which is critical for the 4-week timeline. It produces highly optimized CSS by only bundling what's used, contributing to performance. Tailwind's flexibility allows for creating unique designs without writing custom CSS from scratch, facilitating consistency and maintainability.

2.  BACKEND TECHNOLOGIES

    2.1.  Next.js API Routes
        *   Recommendation: Leverage Next.js API Routes for all backend logic.
        *   Justification: As Next.js is already chosen for the frontend, utilizing its API Routes feature provides a cohesive and efficient full-stack development environment. This eliminates the need for setting up and managing a separate Node.js server, significantly accelerating development for the 4-week deadline. API Routes are suitable for handling Google Maps API requests (proxying to hide API keys), performing score calculations, and interacting with MongoDB for data persistence (e.g., storing game results, user preferences, or location categories). Given there will be "no Multiplayer mode as of now," the scalability provided by Next.js API Routes is sufficient.

    2.2.  Node.js
        *   Recommendation: The runtime environment for Next.js API Routes (implicit).
        *   Justification: Node.js is the underlying runtime for Next.js API Routes, providing a powerful and efficient environment for server-side logic, especially for I/O-bound operations like API calls and database interactions.

    2.3.  Mongoose
        *   Recommendation: An Object Data Modeling (ODM) library for MongoDB.
        *   Justification: Simplifies interaction with MongoDB by providing a schema-based solution to model application data, handling data validation, and offering powerful query capabilities. This abstracts away some of the complexities of working directly with the MongoDB driver, making development faster and more robust.

3.  DATABASE

    3.1.  MongoDB
        *   Recommendation: The chosen NoSQL database.
        *   Justification: Explicitly requested by the project requirements. MongoDB's flexible, document-oriented data model is well-suited for rapidly evolving schemas, which can be beneficial in early-stage development or for storing varied data like game sessions, user profiles (if implemented), and specific location categories. It's performant for the current scope (no multiplayer) and integrates well with Node.js applications via Mongoose.

    3.2.  MongoDB Atlas
        *   Recommendation: Utilize MongoDB Atlas, the cloud-based database service.
        *   Justification: Provides a fully managed, scalable, and highly available MongoDB cluster. This eliminates the need for self-hosting and managing the database, allowing the development team to focus solely on application logic, which is crucial for the limited 4-week timeline.

4.  APIs & EXTERNAL SERVICES

    4.1.  Google Maps Platform
        *   Recommendation: Essential for core game functionality.
        *   Justification: Explicitly required for the game's core features.
            *   Maps JavaScript API: For rendering the interactive map where users pinpoint their guesses and for displaying the actual location.
            *   Street View Service (part of Maps JavaScript API): For dynamically displaying random Street View panoramas. This is preferred over the Static API for a more immersive and interactive experience, aligning with "dynamic web-based" nature.
            *   Geometry Library (part of Maps JavaScript API): For calculating the distance between the user's guessed location and the actual location, directly supporting the "distance based approach" for scoring.
            *   Geocoding API (Optional but recommended): Can be used to convert coordinates to readable addresses for result display, enhancing the user experience.

    4.2.  Vercel
        *   Recommendation: Cloud platform for hosting the Next.js application.
        *   Justification: Vercel is the creator of Next.js and provides an optimized, serverless deployment platform that offers seamless integration with Git repositories (e.g., GitHub). Its automatic deployments on push, global CDN, and serverless functions (for Next.js API Routes) ensure high performance, scalability, and an incredibly simple deployment workflow, which is invaluable for hitting the 4-week deadline.

5.  DEVELOPMENT TOOLS & METHODOLOGIES

    5.1.  Version Control System: Git & GitHub
        *   Recommendation: Use Git for local version control and GitHub for remote repository hosting and collaboration.
        *   Justification: Standard industry practice for code management, collaboration, tracking changes, and enabling continuous integration/continuous deployment (CI/CD) pipelines with Vercel.

    5.2.  Code Quality & Formatting: ESLint & Prettier
        *   Recommendation: Integrate ESLint for linting and Prettier for code formatting.
        *   Justification: Ensures consistent code style across the project, catches potential errors early, and improves code readability and maintainability. This is especially important for rapid development to prevent "technical debt" from accumulating.

    5.3.  Package Manager: npm \\/ Yarn
        *   Recommendation: Use either npm or Yarn for managing project dependencies.
        *   Justification: Standard tools for installing, managing, and updating JavaScript packages required by the project.

    5.4.  Integrated Development Environment (IDE): VS Code
        *   Recommendation: Visual Studio Code.
        *   Justification: A highly popular, feature-rich, and extensible IDE with excellent support for TypeScript, React, Next.js, and a vast ecosystem of extensions that enhance developer productivity.

This technology stack provides a robust, efficient, and cohesive environment tailored to the "Geo-Gusser" project's requirements, ensuring a streamlined development process and timely delivery within the specified 4-week timeline.

## Project Structure
Project Structure: Geo-Gusser

This document outlines the directory and file structure of the Geo-Gusser application. It follows a standard Next.js project layout, enhanced with dedicated directories for components, utilities, and API interactions, promoting modularity and maintainability.

```
.
├── .next/                                # Next.js build output directory (auto-generated)
├── node_modules/                         # Project dependencies (auto-generated)
├── public/                               # Static assets served directly by Next.js
│   ├── images/                           # Application images (logos, backgrounds, icons)
│   └── favicons/                         # Website favicons
├── src/                                  # Main application source code
│   ├── api/                              # Next.js API routes for server-side logic
│   │   ├── locations.js                  # Endpoint to fetch random location data based on categories
│   │   ├── score.js                      # Endpoint to handle score submission/retrieval (future: persistent high scores)
│   │   └── db.js                         # API route for database connection initialization (for demonstration/testing)
│   ├── components/                       # Reusable UI components
│   │   ├── layout/                       # Layout components (e.g., Header, Footer, MainLayout)
│   │   │   └── MainLayout.js             # Wrapper for consistent page structure
│   │   ├── ui/                           # Generic, presentational UI elements (e.g., Button, Modal, Spinner)
│   │   │   ├── Button.js
│   │   │   ├── Modal.js
│   │   │   └── Spinner.js
│   │   ├── game/                         # Components specific to the game play
│   │   │   ├── StreetViewPanorama.js     # Displays the Google Street View panorama
│   │   │   ├── GuessMap.js               # Interactive map for placing guesses
│   │   │   ├── RoundInfo.js              # Displays current round number and score
│   │   │   ├── GameControls.js           # Buttons for game actions (e.g., "Guess", "Next Round")
│   │   │   └── ScoreDisplay.js           # Displays score for the current round
│   │   ├── landing/                      # Components for the landing page
│   │   │   ├── HeroSection.js            # Main hero section with "Play Now" button
│   │   │   └── CategorySelector.js       # Component to select game categories
│   │   └── results/                      # Components for the results/scoreboard page
│   │       ├── Scorecard.js              # Displays detailed score for each round
│   │       ├── OverallScore.js           # Shows the total score and summary
│   │       └── PlayAgainButton.js        # Button to restart the game
│   ├── context/                          # React Context API for global state management
│   │   └── GameContext.js                # Provides game-wide state (e.g., current round, total score, locations)
│   ├── data/                             # Static data used by the application
│   │   ├── categories.json               # Defines available location categories (e.g., "Cities", "Nature")
│   │   └── defaultLocations.json         # Fallback or initial set of predefined locations for development/testing
│   ├── hooks/                            # Custom React hooks for encapsulating logic
│   │   ├── useGame.js                    # Manages main game logic, state, and round progression
│   │   ├── useMap.js                     # Encapsulates map interaction logic (e.g., setting markers, calculating distance)
│   │   └── useStreetView.js              # Manages Street View panorama initialization and control
│   ├── lib/                              # Utility libraries and external service integrations
│   │   ├── googleMaps.js                 # Helper functions for Google Maps API interactions (loading, panorama, geocoding)
│   │   ├── mongo.js                      # MongoDB connection and database utility functions
│   │   └── gameLogic.js                  # Core game logic for scoring, distance calculation, round management
│   ├── pages/                            # Next.js page components (routes)
│   │   ├── _app.js                       # Custom App component for global styles and context providers
│   │   ├── _document.js                  # Custom Document component for modifying initial server-side rendered HTML
│   │   ├── index.js                      # Landing page with "Play Now" button and category selection
│   │   ├── game.js                       # Main game play page
│   │   └── results.js                    # Game results and scoreboard page
│   ├── styles/                           # Global styles and theme definitions
│   │   ├── globals.css                   # Global CSS rules, Tailwind CSS base imports (if used)
│   │   ├── variables.css                 # CSS variables for color palette, typography etc.
│   │   └── components.css                # Component-specific styles (if not using CSS modules or Tailwind utilities)
│   ├── utils/                            # General utility functions and constants
│   │   ├── constants.js                  # Application-wide constants (e.g., number of rounds, score multipliers)
│   │   └── helpers.js                    # Miscellaneous helper functions (e.g., data formatting, distance calculation)
│   └── types/                            # TypeScript type definitions (even if JS, good for documentation)
│       └── game.d.ts                     # Type definitions for game objects, states, etc.
├── .env.local                            # Environment variables (local development)
├── jsconfig.json                         # JavaScript configuration (for absolute imports, aliases)
├── next.config.js                        # Next.js configuration file
├── package.json                          # Project dependencies and scripts
├── postcss.config.js                     # PostCSS configuration (e.g., for Tailwind CSS)
├── tailwind.config.js                    # Tailwind CSS configuration (if used for "Minimal and Beautiful" design)
└── README.md                             # Project README file
```

**Directory Explanations:**

*   **`.next/`**: This directory is automatically generated by Next.js during the build process. It contains the optimized production build of your application.
*   **`node_modules/`**: Contains all the third-party libraries and packages installed for the project. This directory is managed by `npm` or `yarn`.
*   **`public/`**: This directory is used to serve static assets directly. Files placed here are accessible from the root URL of your application (e.g., `/images/logo.png`).
    *   `images/`: Stores static image assets like logos, background images, and general graphics.
    *   `favicons/`: Contains various favicon sizes and types for browser tabs.
*   **`src/`**: The primary source code directory for the application. Encapsulates all functional code for clarity and organization.
    *   **`api/`**: Next.js API routes. These files run on the server-side, allowing you to create backend endpoints within your Next.js application.
        *   `locations.js`: Handles requests for fetching random Street View locations, potentially filtering by category. In a future iteration, this might connect to a database for a wider range of locations.
        *   `score.js`: Currently reserved for potential future use (e.g., saving high scores to MongoDB if accounts are implemented). For initial release, scoring logic is client-side.
        *   `db.js`: A simple API endpoint to test or verify database connection (MongoDB) on the server, ensuring `lib/mongo.js` is functional.
    *   **`components/`**: Houses all reusable React components, categorized for better management.
        *   `layout/`: Components that define the overall structure and persistent elements of the application pages (e.g., header, footer, main content wrapper).
        *   `ui/`: Generic, presentational UI components that can be used across different parts of the application and are independent of specific game logic (e.g., buttons, modals, input fields).
        *   `game/`: Components specifically designed for the core game experience, such as the Street View display, the guess map, and game controls.
        *   `landing/`: Components that constitute the landing page of the application, including hero sections and interactive elements for starting the game.
        *   `results/`: Components responsible for displaying the game results, including round-by-round breakdowns and overall scores.
    *   **`context/`**: Contains React Context API providers for managing global state that needs to be accessible across multiple components without prop-drilling.
        *   `GameContext.js`: Manages the global state related to the game, such as the current round, player's score, remaining time, and location data.
    *   **`data/`**: Stores static JSON data used by the application, which might eventually be migrated to a database as the project scales.
        *   `categories.json`: Defines the different geographical categories players can choose from (e.g., \"World Cities\", \"Natural Landscapes\").
        *   `defaultLocations.json`: A static list of locations used for game rounds, especially useful for development and as a fallback if dynamic location fetching isn't yet fully implemented.
    *   **`hooks/`**: Custom React hooks to encapsulate reusable stateful logic and side effects.
        *   `useGame.js`: A central hook for managing the complex state and logic of the game, including starting new rounds, processing guesses, and calculating scores.
        *   `useMap.js`: Handles the logic for interacting with the Google Maps instance, such as rendering markers, calculating distances between points, and centering the map.
        *   `useStreetView.js`: Manages the lifecycle and interactions with the Google Street View Panorama instance.
    *   **`lib/`**: Contains utility libraries and integrations with external services. These functions are often independent of React components and can be used on both client and server sides.
        *   `googleMaps.js`: Provides functions for loading the Google Maps API script, creating panorama and map instances, and handling geocoding requests.
        *   `mongo.js`: Contains the logic for connecting to and interacting with the MongoDB database. This will be used for persistent data storage (e.g., high scores, user profiles, if optional accounts are pursued).
        *   `gameLogic.js`: Implements the core game mechanics, including distance calculation between guess and actual location, score computation based on distance, and round progression rules.
    *   **`pages/`**: Next.js uses this directory to define routes. Each file in this directory typically corresponds to a unique URL path.
        *   `_app.js`: A custom App component that wraps all pages. It's used for global layouts, injecting global CSS, and persisting state across page navigation.
        *   `_document.js`: A custom Document component used to augment the `<html>` and `<body>` tags of the server-rendered HTML. Useful for injecting custom scripts or font links.
        *   `index.js`: The root page of the application, serving as the landing page with options to start the game.
        *   `game.js`: The main page where the game is played, displaying the Street View panorama and the interactive map.
        *   `results.js`: The page displaying the game's final scores and round summaries after all rounds are completed.
    *   **`styles/`**: Dedicated to global styling and theme definitions.
        *   `globals.css`: Contains global CSS resets, base styles, and potentially imports for frameworks like Tailwind CSS.
        *   `variables.css`: Defines CSS variables for consistent theming, such as color palettes, font sizes, and spacing.
        *   `components.css`: Contains specific styles for components, if not using a utility-first CSS framework like Tailwind or CSS modules.
    *   **`utils/`**: General-purpose utility functions that are not specific to any single component or module.
        *   `constants.js`: Stores application-wide constants, such as the number of rounds per game, maximum score, or API rate limits.
        *   `helpers.js`: A collection of miscellaneous helper functions, including mathematical calculations (e.g., distance between two coordinates), data transformations, and formatting.
    *   **`types/`**: (Recommended for clarity, even in JavaScript projects) Contains TypeScript type definition files.
        *   `game.d.ts`: Defines interfaces and types for game-related objects, states, and properties, aiding in code clarity and development.
*   **`.env.local`**: Stores environment variables specific to the local development environment, such as API keys (e.g., Google Maps API Key) and database connection strings. Not committed to version control.
*   **`jsconfig.json`**: Configuration file for JavaScript that helps IDEs understand the project structure, enabling features like absolute imports and path aliases.
*   **`next.config.js`**: The configuration file for Next.js, allowing customization of build behavior, image optimization, environment variables, and more.
*   **`package.json`**: Defines project metadata, scripts for running development and build processes, and lists all project dependencies.
*   **`postcss.config.js`**: Configuration file for PostCSS, a tool for transforming CSS with JavaScript plugins. Commonly used with Tailwind CSS for optimizing and processing styles.
*   **`tailwind.config.js`**: Configuration file for Tailwind CSS, where custom designs (colors, typography, spacing) and component variants are defined to achieve the \"Minimal and Beautiful\" UI.
*   **`README.md`**: Provides a general overview of the project, setup instructions, and deployment details.

## Database Schema Design
SCHEMADESIGN

This section outlines the database schema design for the Geo-Gusser application. Given the project's requirements, specifically the choice of MongoDB as the database and the nature of the data (document-oriented, flexible schema), the design focuses on collections, their structure, data types, and relationships. The schema is designed to support core game mechanics, user interaction (including optional accounts), and basic analytics.



**1. Database Choice & Overall Approach**



*   **Database System**: MongoDB (NoSQL, Document-Oriented Database)

*   **Approach**: Data is organized into collections, where each document within a collection represents a specific entity. Embedding related data (e.g., game rounds within a game session) is leveraged for performance and data locality, aligning with MongoDB best practices. Relationships are primarily established through direct embedding or by referencing ObjectIDs between collections.



**2. Core Collections & Schema Definitions**



**2.1. `users` Collection**



*   **Purpose**: Stores information about registered players. Account creation is optional, so this collection will only contain data for users who choose to register.

*   **Structure**:

    *   `_id`: `ObjectId`

        *   **Description**: Unique identifier for the user. Primary key.

        *   **Constraints**: Auto-generated by MongoDB.

    *   `username`: `String`

        *   **Description**: User's chosen display name.

        *   **Constraints**: Optional (can be null/missing if user only registers via email, or if a default is provided), Unique (if present), Min length 3, Max length 30.

    *   `email`: `String`

        *   **Description**: User's email address. Used for login and identification.

        *   **Constraints**: Required (for registered users), Unique, Valid email format.

    *   `passwordHash`: `String`

        *   **Description**: Hashed password for security.

        *   **Constraints**: Required (for registered users).

    *   `createdAt`: `Date`

        *   **Description**: Timestamp when the user account was created.

    *   `updatedAt`: `Date`

        *   **Description**: Timestamp of the last update to the user's profile.

    *   `gamesPlayed`: `Number`

        *   **Description**: Total number of games completed by this user.

        *   **Default**: 0

    *   `totalScore`: `Number`

        *   **Description**: Sum of scores from all completed games by this user.

        *   **Default**: 0

    *   `bestScore`: `Number`

        *   **Description**: Highest single game score achieved by this user.

        *   **Default**: 0



*   **Indexes**:

    *   `_id`: Primary key (default index).

    *   `email`: Unique index (for efficient login and ensuring uniqueness).

    *   `username`: Unique index (sparse, if optional and can be missing; for efficient lookup).



**2.2. `categories` Collection**



*   **Purpose**: Defines available game modes based on location categories (e.g., "World Standard", "US Cities"). These categories will influence how random panoramas are selected via the Google Maps API.

*   **Structure**:

    *   `_id`: `ObjectId`

        *   **Description**: Unique identifier for the category. Primary key.

    *   `name`: `String`

        *   **Description**: Display name of the category (e.g., "World Standard", "US Cities").

        *   **Constraints**: Required, Unique.

    *   `description`: `String`

        *   **Description**: A brief description of the category.

    *   `queryConfig`: `Object`

        *   **Description**: Configuration object that the backend logic uses to guide Google Maps API requests for panorama generation (e.g., `{ "area": "global" }`, `{ "area": "usa_cities" }`).

        *   **Example**: `{ "area": "global" }`, `{ "restrictToCountry": "US" }`, `{ "tags": ["city", "landmark"] }`

    *   `isActive`: `Boolean`

        *   **Description**: Indicates if the category is currently available for play.

        *   **Default**: `true`



*   **Indexes**:

    *   `_id`: Primary key (default index).

    *   `name`: Unique index.



**2.3. `gameSessions` Collection**



*   **Purpose**: Represents a single completed game session, which consists of multiple rounds (typically 5). This collection tracks the overall game progress, score, and individual round details.

*   **Structure**:

    *   `_id`: `ObjectId`

        *   **Description**: Unique identifier for the game session. Primary key.

    *   `userId`: `ObjectId`

        *   **Description**: Reference to the `_id` of the `users` collection if the game was played by a registered user.

        *   **Constraints**: Optional (can be `null` or missing for anonymous players).

    *   `categoryId`: `ObjectId`

        *   **Description**: Reference to the `_id` of the `categories` collection, indicating the game mode/category played.

        *   **Constraints**: Required.

    *   `startTime`: `Date`

        *   **Description**: Timestamp when the game session started.

    *   `endTime`: `Date`

        *   **Description**: Timestamp when the game session ended (after 5 rounds or user quit).

    *   `totalScore`: `Number`

        *   **Description**: The final accumulated score for this game session across all rounds.

        *   **Constraints**: Calculated based on individual round scores.

    *   `isCompleted`: `Boolean`

        *   **Description**: Indicates if the game session was played to completion (all 5 rounds).

        *   **Default**: `false`

    *   `rounds`: `Array` of `Object` (Embedded Documents)

        *   **Description**: An array containing details for each individual round played within this session. There will typically be 5 such embedded documents.

        *   **Embedded Round Structure**:

            *   `roundNumber`: `Number`

                *   **Description**: The sequential number of the round within the game (1 to 5).

            *   `panoramaLocation`: `Object`

                *   **Description**: The actual latitude and longitude of the Street View panorama.

                *   **Structure**: `{ "lat": Number, "lng": Number }`

            *   `guessLocation`: `Object`

                *   **Description**: The user's guessed latitude and longitude on the map.

                *   **Structure**: `{ "lat": Number, "lng": Number }`

            *   `distanceKm`: `Number`

                *   **Description**: The calculated distance in kilometers between the `panoramaLocation` and `guessLocation`.

            *   `score`: `Number`

                *   **Description**: The score awarded for this specific round, based on `distanceKm`.

            *   `timeTakenMs`: `Number`

                *   **Description**: Time taken by the user to make a guess in this round, in milliseconds.



*   **Indexes**:

    *   `_id`: Primary key (default index).

    *   `userId`: Index on `userId` (sparse, for efficient lookup of games by a specific user).

    *   `startTime`: Index for querying recent games or sorting.

    *   `isCompleted`: Index for filtering completed vs. in-progress games.

    *   `categoryId`: Index for querying games by specific categories.



**3. Relationships Overview**



*   **One-to-Many (1:N) User to GameSession**: A single user (`users._id`) can have multiple game sessions (`gameSessions.userId`). This relationship is established via the `userId` field in the `gameSessions` collection, which references the `_id` of a document in the `users` collection. For anonymous games, `userId` will be absent or null.

*   **One-to-Many (1:N) Category to GameSession**: A single category (`categories._id`) can be associated with multiple game sessions (`gameSessions.categoryId`). This is established via the `categoryId` field in the `gameSessions` collection.

*   **One-to-Many (1:N) GameSession to Round (Embedded)**: A single game session (`gameSessions` document) contains multiple individual rounds (`gameSessions.rounds` array). This is an embedded relationship, where `Round` documents are part of the `GameSession` document, providing high data locality and efficient retrieval of all round details along with the parent game session.



**4. Indexing Strategy**



Indexes are crucial for efficient data retrieval. The proposed indexes are designed to optimize common queries:



*   **User Lookups**: `email` and `username` indexes on `users` collection for authentication and profile retrieval.

*   **Game History**: `userId` index on `gameSessions` to quickly fetch all games played by a specific user. `startTime` index for sorting or filtering games by date.

*   **Game Mode Filtering**: `categoryId` index on `gameSessions` to allow filtering games by the type of category played.



**5. Data Type Considerations**



*   **Numbers**: All numerical values (scores, distances, round numbers, counts) are stored as MongoDB's `Number` type (typically double-precision floating-point, sufficient for scores and distances).

*   **Dates**: All timestamps are stored as `Date` objects, allowing for efficient date-based queries and sorting.

*   **Location Coordinates**: Latitude and longitude are stored as embedded objects `{ lat: Number, lng: Number }`. This is a clear and commonly used format for geographical points.

*   **Arrays**: Used for `rounds` within `gameSessions`, enabling efficient storage and retrieval of all round details as a single unit.



**6. Scalability and Performance Notes**



*   **Embedding `Rounds`**: Embedding `rounds` directly within the `gameSessions` document ensures that all data for a single game (header info and all round details) is retrieved in a single read operation, which is highly efficient for typical game summary display. Given that there are a fixed and small number of rounds (5) per game, this embedding strategy is optimal and avoids the need for joins.

*   **No Multiplayer**: The absence of a multiplayer mode simplifies concurrent write concerns significantly, as game sessions are primarily isolated to a single user.

*   **Analytics**: Basic analytics (total games, total score, best score) are pre-calculated and stored on the `users` document. More complex analytics would involve aggregation pipelines on the `gameSessions` collection.

## User Flow
USERFLOW

This document details the complete user journey through the Geo-Gusser application, from initial entry to game completion, outlining key interactions, screen states, and system responses. It focuses on the core gameplay loop, emphasizing the "Minimal and Beautiful" UI/UX preference and the optional account creation.

**UF.001: Landing Page (Entry Point)**
*   **Description:** The initial gateway to Geo-Gusser. This page is designed to be visually appealing, providing a clear call to action for users to start playing immediately. Account creation/login is available but not mandatory for game access.
*   **Wireframe Description:**
    *   **Header:** Contains the "Geo-Gusser" logo/title, potentially a subtle tagline. Optional, less prominent "Log In" or "Sign Up" links are present, typically in the top-right or bottom-left corner.
    *   **Hero Section:** A large, captivating background image or animation (e.g., a beautiful, rotating panorama or diverse global landscapes) that embodies the game's theme. This image should be high-quality and unique.
    *   **Call to Action (CTA):** A prominent, centrally located "Play Now" button. This button is the primary means of entry into the game for casual users.
    *   **Footer (Optional):** Minimal links like "About," "Privacy Policy."
*   **Interaction Patterns:**
    *   **User Action:** User navigates to the application's base URL (e.g., `https://geo-gusser.com`).
    *   **System Response:** The Landing Page (UF.001) is displayed, loading all visual assets and interactive elements.
    *   **User Action:** User clicks the "Play Now" button.
    *   **System Response:** The system initiates the process to start a new game, navigating the user to the Game Setup / Mode Selection screen (UF.002).
    *   **User Action:** User clicks "Log In" or "Sign Up" (optional).
    *   **System Response:** User is redirected to the respective authentication flow (Login/Registration screens - outside the scope of this core game flow documentation).

**UF.002: Game Setup / Mode Selection**
*   **Description:** This screen allows the user to configure their game session, primarily by selecting a location category, before commencing gameplay. This ensures that the user can play based on their preferred difficulty or theme.
*   **Wireframe Description:**
    *   **Header:** "Choose Your Adventure" or "Start a New Game" title.
    *   **Category Selection Area:** A grid or list of distinct location categories. Each category (e.g., "World Explorer," "Cities of the World," "Rural Roads," "Landmarks") is presented as a clickable card or button, possibly with a small icon or preview image.
    *   **Game Rules/Info (Optional):** A concise text block explaining the game basics (e.g., "5 Rounds," "Score based on distance").
    *   **Start Game Button:** A large button, initially disabled, that becomes active once a location category has been chosen.
*   **Interaction Patterns:**
    *   **User Action:** User arrives from UF.001 (after clicking "Play Now").
    *   **System Response:** The Game Setup screen is displayed, presenting various location categories.
    *   **User Action:** User clicks on one of the available location category cards/buttons.
    *   **System Response:** The selected category is visually highlighted (e.g., by a border, background change). The "Start Game" button becomes enabled.
    *   **User Action:** User clicks the enabled "Start Game" button.
    *   **System Response:** The system performs the following: initializes a new game session, randomly selects the first Street View panorama based on the chosen category (using Google Maps API), and transitions the user to the Game Play screen (UF.003). For an unauthenticated user, a temporary game session is created.

**UF.003: Game Play (Per Round)**
*   **Description:** This is the core interactive game screen, repeated for 5 rounds. Users analyze a Street View image and pinpoint their guess on an interactive map. Scoring occurs after each guess.
*   **Wireframe Description:**
    *   **Top Bar/Header:** Clearly displays the current round number (e.g., "Round 1/5"). Could also show a cumulative score or round-specific score after a guess.
    *   **Street View Panorama Area (Left/Top):** The dominant section, embedding the Google Street View component. Allows users to pan (360 degrees), zoom in/out, and navigate subtly (if the panorama allows) to gather clues. No map overlay or markers should be visible in this section until after a guess.
    *   **Interactive Map Area (Right/Bottom):** An embedded Google Maps component. Users can pan and zoom across the global map. There is a mechanism (e.g., click/tap) to place a single, draggable marker (pin) representing their guess. The "Make Guess" button is associated with this section.
    *   **"Make Guess" Button:** A prominent button, initially disabled, that activates once a guess pin has been placed on the map.
    *   **Post-Guess Display:** After a guess is submitted, this area updates to show:
        *   The user's guess pin.
        *   The actual location pin (distinctly colored).
        *   A line connecting the guess to the actual location.
        *   Text displaying the calculated distance between the guess and actual location (e.g., "You were 150 km away!").
        *   Text displaying the score awarded for the round (based on distance).
        *   A "Next Round" or "Continue" button.
*   **Interaction Patterns:**
    *   **User Action:** User arrives from UF.002, or from a previous round's completion in UF.003.
    *   **System Response:** A new random Street View panorama is loaded and displayed. The interactive map is reset (no previous markers), and the "Make Guess" button is disabled. The round counter updates.
    *   **User Action:** User explores the Street View panorama (panning, zooming).
    *   **System Response:** The Street View viewport updates dynamically based on user interaction.
    *   **User Action:** User clicks or taps on the interactive map to place their guess pin.
    *   **System Response:** A draggable marker (pin) appears at the clicked location. The "Make Guess" button becomes active.
    *   **User Action:** User drags the guess pin to refine its position on the map.
    *   **System Response:** The marker's position updates in real-time on the map.
    *   **User Action:** User clicks the active "Make Guess" button.
    *   **System Response:**
        1.  The system captures the user's guessed coordinates and the actual location's coordinates.
        2.  It calculates the distance between these two points using Google Maps API's geometry library or similar algorithm.
        3.  A score is calculated for the round based on this distance (closer guess = higher score).
        4.  The actual location pin and a connecting line are displayed on the map for visual feedback.
        5.  The distance and score for the round are presented to the user.
        6.  The "Make Guess" button is replaced by or complemented with a "Next Round" or "Continue" button.
    *   **User Action:** User clicks "Next Round" / "Continue" button.
    *   **System Response:**
        *   **If current round < 5:** The system clears the map and Street View, loads a new random panorama, increments the round counter, and re-initializes for the next round (repeating UF.003).
        *   **If current round == 5:** The system aggregates all round scores and transitions the user to the Game Results / Scoreboard screen (UF.004).

**UF.004: Game Results / Scoreboard**
*   **Description:** The conclusive screen after 5 rounds, presenting a summary of the user's performance, including total score and per-round details. It provides options to play again or return to the landing page.
*   **Wireframe Description:**
    *   **Header:** "Game Over!" or "Your Results" title.
    *   **Total Score Display:** A large, prominent display of the user's cumulative score across all 5 rounds.
    *   **Round-by-Round Breakdown:** A list or table detailing each of the 5 rounds. For each round, it should display:
        *   Round Number.
        *   Distance (e.g., "150 km from actual").
        *   Score awarded for that round.
        *   (Optional but Recommended): A small, static map snippet or screenshot showing the guess vs. actual location for that specific round, providing visual context to the score.
    *   **Action Buttons:**
        *   "Play Again" button: Allows the user to quickly start a new game session.
        *   "Home" button: Returns the user to the initial landing page.
*   **Interaction Patterns:**
    *   **User Action:** User arrives from UF.003 after completing the 5th round.
    *   **System Response:** The Game Results screen is displayed, showing the total score and the detailed breakdown of each round's performance.
    *   **User Action:** User clicks the "Play Again" button.
    *   **System Response:** The system initiates a new game flow, navigating the user back to the Game Setup / Mode Selection screen (UF.002).
    *   **User Action:** User clicks the "Home" button.
    *   **System Response:** The user is redirected to the Landing Page (UF.001).

**Key System Processes (Underlying Flows):**
*   **Location Generation:** For each round, the backend (Next.js server-side logic/API routes) will randomly select a valid geographical coordinate that has Google Street View coverage, constrained by the selected game category. This coordinate is then passed to the frontend for displaying the panorama and determining the actual location for scoring.
*   **Score Calculation:** After each guess, the backend calculates the geodesic distance between the user's guessed coordinates and the actual location's coordinates. This distance is then translated into a score based on a predefined inverse relationship (e.g., closer distance = higher score, with a maximum score for near-perfect guesses and rapidly diminishing returns as distance increases).
*   **Data Storage (Optional):** For authenticated users, game results (scores, locations) could be optionally stored in MongoDB for tracking personal bests or statistics. For unauthenticated users, game results are ephemeral for the current session only.

## Styling Guidelines
STYLING GUIDELINES DOCUMENT FOR GEO-GUSSER

1. DESIGN PHILOSOPHY
Our core design philosophy for Geo-Gusser is centered around three key pillars: Minimal, Beautiful, and Unique. We aim to deliver an immersive and engaging user experience through clean aesthetics, intuitive interactions, and a distinct visual identity that sets Geo-Gusser apart. Every design decision will prioritize user clarity, visual appeal, and smooth performance across all devices. The interface should feel modern, polished, and inviting to a broad audience, fostering an enjoyable and focused gameplay experience.

2. DESIGN PRINCIPLES

    2.1. Clarity & Simplicity
        - Uncluttered interfaces: Focus on essential elements, removing visual noise.
        - Intuitive navigation: Users should effortlessly understand how to play and interact.
        - Direct communication: Text and visual cues should be concise and easily understood.

    2.2. Consistency
        - Predictable elements: Buttons, inputs, and other UI components should behave and appear consistently throughout the application.
        - Coherent user flow: Maintain a logical and familiar journey from landing page to game, and results.
        - Unified visual language: Ensure colors, typography, and spacing are applied uniformly.

    2.3. Elegance & Beauty
        - Refined aesthetics: Employ a thoughtful color palette, sophisticated typography, and harmonious layouts.
        - Attention to detail: Subtle animations, smooth transitions, and polished micro-interactions enhance the user experience.
        - High-quality visuals: Use crisp imagery and clean graphical elements, especially for maps and street views.

    2.4. Responsiveness
        - Seamless experience across devices: Design for optimal viewing and interaction on desktops, tablets, and mobile phones.
        - Flexible layouts: Components and content should adapt gracefully to varying screen sizes and orientations.

    2.5. Feedback
        - Immediate and clear: Users should receive prompt visual or haptic feedback for every interaction (e.g., button clicks, successful guesses, errors).
        - Informative: Feedback should communicate the state or outcome of an action effectively.

3. COLOR PALETTE
The color palette is chosen to reflect a modern, clean, and inviting aesthetic. It balances calm neutrals with vibrant accents to highlight key interactions and information.

    3.1. Primary Accent: `#36D1DC` (Aqua/Teal)
        - Usage: Primary calls-to-action (e.g., "Play Now" button), active navigation states, interactive map highlights (guess radius, selected markers), important progress indicators, branding elements.
        - Represents: Freshness, clarity, progress, distinctiveness.

    3.2. Secondary Accent: `#FFC107` (Amber)
        - Usage: Secondary buttons, subtle highlights, warning messages, "score board" call-outs, complementary interactive elements.
        - Represents: Energy, attention, warmth, success (in certain contexts).

    3.3. Neutrals
        - Background Primary: `#F8F9FA` (Off-white/Light Gray)
            - Usage: Main application background, large content areas.
            - Represents: Cleanliness, space, minimalism.
        - Surface/Card Background: `#FFFFFF` (Pure White)
            - Usage: Component cards, modals, game UI containers.
            - Represents: Purity, focus, elevated elements.
        - Text Primary: `#212529` (Dark Charcoal)
            - Usage: Main body text, headings, primary labels.
            - Represents: Readability, professionalism.
        - Text Secondary: `#6C757D` (Muted Gray)
            - Usage: Sub-text, captions, less critical information, placeholder text.
            - Represents: Subtlety, supporting detail.
        - Borders & Dividers: `#DEE2E6` (Light Gray)
            - Usage: Separators, input field borders, card outlines, subtle structural elements.
            - Represents: Structure, division without distraction.

    3.4. System Colors (Semantic)
        - Success: `#28A745` (Green)
            - Usage: Positive feedback messages, correct answers, successful actions.
        - Error: `#DC3545` (Red)
            - Usage: Error messages, invalid input, negative feedback.
        - Warning: `#FFC107` (Amber - re-used Secondary Accent)
            - Usage: Cautionary messages, important notices.

    3.5. Map Overlays
        - Guess Area Highlight: A semi-transparent overlay using Primary Accent (e.g., `#36D1DC` with 30-50% opacity) on the interactive map to visualize the user"s guess radius.
        - Markers: Distinct colors for "Your Guess" and "Actual Location" (e.g., Primary Accent for guess, a contrasting shade like a darker blue or specific red for actual location, if not using semantic colors).

4. TYPOGRAPHY
Typography is carefully selected to ensure high readability for all users while contributing to the "Minimal, Beautiful, Unique" aesthetic. We primarily use two font families from Google Fonts.

    4.1. Font Families
        - **Headings & UI Elements:** `Montserrat` (Sans-serif)
            - Usage: All headings (H1-H4), button text, prominent UI labels, score displays.
            - Characteristics: Modern, geometric, strong, provides a unique touch without being overly decorative.
            - Weights: Regular (400), Semi-Bold (600), Bold (700) - use sparingly for hierarchy.
        - **Body Text & Paragraphs:** `Lato` (Sans-serif)
            - Usage: All body paragraphs, detailed descriptions, captions, input field text.
            - Characteristics: Highly readable, clean, versatile, excellent for extended reading.
            - Weights: Regular (400), Light (300), Bold (700).

    4.2. Font Sizing (Based on 16px root font-size for `rem` units)
        - H1: `3rem` (48px) - Major page titles, final score display.
        - H2: `2.5rem` (40px) - Section headers on game page, scoreboard titles.
        - H3: `2rem` (32px) - Sub-sections, important prompts.
        - H4: `1.5rem` (24px) - Component titles, prominent labels.
        - Body Large: `1.125rem` (18px) - Key information, larger paragraphs.
        - Body Regular: `1rem` (16px) - Standard paragraph text, main content.
        - Small Text: `0.875rem` (14px) - Captions, metadata, disclaimers.

    4.3. Line Height
        - Body Text: `1.5` (150% of font size) for optimal readability.
        - Headings: `1.2` for a more compact and impactful appearance.

    4.4. Letter Spacing
        - Generally set to `normal` or `0`.
        - Very subtle tightening (`-0.02em` to `-0.05em`) for large headings can enhance visual appeal.

5. ICONOGRAPHY
Icons will be used sparingly to enhance comprehension and visual appeal, not to replace text entirely.

    5.1. Style
        - Outline/Stroke style: Clean, modern, and minimal.
        - Consistent stroke weight: Ensures uniformity across all icons.
        - Simple, recognizable shapes.

    5.2. Source
        - Preference for a curated icon library (e.g., Feather Icons, Heroicons) or custom-designed SVG icons to maintain a unique touch.

    5.3. Usage
        - Supporting navigation (e.g., info icons, settings).
        - Visualizing actions (e.g., play, submit guess).
        - Enhancing gamification elements (e.g., score, distance icons).

6. COMPONENT GUIDELINES (GENERAL)

    6.1. Buttons
        - **Primary Buttons:** Solid fill with `Primary Accent` color (`#36D1DC`), white text (`#FFFFFF`). Slightly rounded corners (e.g., 6px radius).
        - **Secondary Buttons:** Outline style, with `Primary Accent` color for border and text. Transparent fill. Slightly rounded corners.
        - **Hover/Focus States:** Subtle visual feedback (e.g., slight background color darkening for primary, border thickening or color change for secondary, gentle scale animation).
        - **Disabled State:** Reduced opacity, no hover effect.

    6.2. Input Fields
        - Minimalist design: Light border (`#DEE2E6`), sufficient padding.
        - Focus State: Border color changes to `Primary Accent` (`#36D1DC`).
        - Error State: Border color changes to `Error` color (`#DC3545`).

    6.3. Cards & Containers
        - Background: `Surface/Card Background` (`#FFFFFF`) or `Background Primary` (`#F8F9FA`).
        - Depth: Subtle box-shadow (e.g., `0px 4px 10px rgba(0, 0, 0, 0.05)`) for elevated elements.
        - Corners: Consistent small `border-radius` (e.g., 8px).

    6.4. Map & Street View Integration
        - **Map Styling:** Custom Google Maps styling (JSON) to match the overall aesthetic – muted colors for less prominent map features, allowing user markers and guess areas to stand out.
        - **Markers:** Clearly distinguishable markers for "Your Guess" and "Actual Location". Custom icons may be used.
        - **Interactivity:** Smooth panning and zooming, clear visual feedback for map clicks.

7. LAYOUT AND SPACING

    7.1. Grid System
        - A flexible, responsive grid system (e.g., CSS Grid or Flexbox-based) will be used to ensure consistent alignment and distribution of elements across different screen sizes.

    7.2. Spacing Units
        - All spacing (padding, margins, gaps) will adhere to a base unit system (e.g., `8px` as the fundamental unit), ensuring visual harmony and consistency. Common values: `8px`, `16px`, `24px`, `32px`, `48px`, `64px`.

    7.3. Visual Hierarchy
        - Utilize white space generously to create a sense of openness and guide the user"s eye.
        - Size, color, and positioning will be used strategically to emphasize important information and actions.

8. INTERACTIONS AND FEEDBACK

    8.1. Hover & Focus States
        - Every interactive element (buttons, links, input fields) will have clearly defined and consistent hover and focus states to indicate interactivity and aid keyboard navigation.

    8.2. Loading States
        - Provide immediate visual feedback during loading times. This can include subtle spinners, skeleton loaders for content areas, or progress bars, to manage user expectations.

    8.3. Transitions & Animations
        - Use subtle, smooth, and purposeful transitions and animations to enhance the "beautiful" and "unique" aspects. Examples include:
            - Smooth fades or slides for route changes.
            - Gentle scaling or color changes on button hovers.
            - Animated score updates.
            - Seamless transitions between game rounds.
        - Animations should be performant and not distract from the core gameplay.

    8.4. Error Messages
        - Clear, concise, and actionable error messages will be displayed using the `Error` color (`#DC3545`). They should guide the user on how to resolve the issue.

9. ACCESSIBILITY (A11Y)

    9.1. Color Contrast
        - Ensure sufficient color contrast between text and background elements to meet WCAG 2.1 AA standards, making content readable for users with visual impairments.

    9.2. Focus Indicators
        - Maintain clear and visible focus outlines for all interactive elements to support keyboard-only navigation.

    9.3. Semantic HTML
        - Utilize appropriate semantic HTML5 elements to structure content, improving screen reader compatibility and overall accessibility.

    9.4. Alt Text
        - Provide descriptive `alt` text for all meaningful images and interactive elements where appropriate.

10. RESPONSIVENESS

    10.1. Mobile-First Approach
        - Design and develop with a mobile-first mindset, ensuring the core experience is optimized for smaller screens before scaling up to larger devices.

    10.2. Breakpoints
        - Standard breakpoints will be used (e.g., mobile, tablet, desktop) to define how layouts and components adapt.
        - Mobile: up to 767px
        - Tablet: 768px - 1023px
        - Desktop: 1024px and up

    10.3. Touch-Friendly Targets
        - Ensure that all interactive elements (buttons, links, map controls) have sufficiently large touch targets for comfortable mobile interaction.
