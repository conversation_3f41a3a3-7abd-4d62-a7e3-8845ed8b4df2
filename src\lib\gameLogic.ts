import { Location, GameRound } from '@/types/game';
import { GAME_CONFIG, SCORING } from '@/utils/constants';

/**
 * Calculate the distance between two geographical points using the Haversine formula
 * @param point1 First location
 * @param point2 Second location
 * @returns Distance in kilometers
 */
export function calculateDistance(point1: Location, point2: Location): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(point2.lat - point1.lat);
  const dLng = toRadians(point2.lng - point1.lng);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(point1.lat)) * Math.cos(toRadians(point2.lat)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return Math.round(distance * 100) / 100; // Round to 2 decimal places
}

/**
 * Convert degrees to radians
 */
function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Calculate score based on distance between guess and actual location
 * @param distanceKm Distance in kilometers
 * @returns Score between 0 and MAX_SCORE_PER_ROUND
 */
export function calculateScore(distanceKm: number): number {
  const { MAX_SCORE_PER_ROUND } = GAME_CONFIG;
  const { PERFECT_DISTANCE, GOOD_DISTANCE, FAIR_DISTANCE, POOR_DISTANCE } = SCORING;
  
  if (distanceKm <= PERFECT_DISTANCE) {
    return MAX_SCORE_PER_ROUND;
  }
  
  if (distanceKm <= GOOD_DISTANCE) {
    // Linear interpolation between perfect and good
    const ratio = (GOOD_DISTANCE - distanceKm) / (GOOD_DISTANCE - PERFECT_DISTANCE);
    return Math.round(MAX_SCORE_PER_ROUND * 0.8 + (MAX_SCORE_PER_ROUND * 0.2 * ratio));
  }
  
  if (distanceKm <= FAIR_DISTANCE) {
    // Linear interpolation between good and fair
    const ratio = (FAIR_DISTANCE - distanceKm) / (FAIR_DISTANCE - GOOD_DISTANCE);
    return Math.round(MAX_SCORE_PER_ROUND * 0.4 + (MAX_SCORE_PER_ROUND * 0.4 * ratio));
  }
  
  if (distanceKm <= POOR_DISTANCE) {
    // Linear interpolation between fair and poor
    const ratio = (POOR_DISTANCE - distanceKm) / (POOR_DISTANCE - FAIR_DISTANCE);
    return Math.round(MAX_SCORE_PER_ROUND * 0.1 + (MAX_SCORE_PER_ROUND * 0.3 * ratio));
  }
  
  // Beyond poor distance, minimum score
  return Math.round(MAX_SCORE_PER_ROUND * 0.1);
}

/**
 * Process a round by calculating distance and score
 * @param panoramaLocation Actual location of the panorama
 * @param guessLocation User's guessed location
 * @param roundNumber Current round number
 * @param timeTakenMs Time taken to make the guess
 * @returns Completed round data
 */
export function processRound(
  panoramaLocation: Location,
  guessLocation: Location,
  roundNumber: number,
  timeTakenMs: number
): GameRound {
  const distanceKm = calculateDistance(panoramaLocation, guessLocation);
  const score = calculateScore(distanceKm);
  
  return {
    roundNumber,
    panoramaLocation,
    guessLocation,
    distanceKm,
    score,
    timeTakenMs,
  };
}

/**
 * Calculate total score from all rounds
 * @param rounds Array of completed rounds
 * @returns Total score
 */
export function calculateTotalScore(rounds: GameRound[]): number {
  return rounds.reduce((total, round) => total + (round.score || 0), 0);
}
