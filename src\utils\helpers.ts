/**
 * Format distance for display
 * @param distanceKm Distance in kilometers
 * @returns Formatted distance string
 */
export function formatDistance(distanceKm: number): string {
  if (distanceKm < 1) {
    return `${Math.round(distanceKm * 1000)}m`;
  }
  if (distanceKm < 10) {
    return `${distanceKm.toFixed(1)}km`;
  }
  return `${Math.round(distanceKm)}km`;
}

/**
 * Format score for display
 * @param score Score value
 * @returns Formatted score string
 */
export function formatScore(score: number): string {
  return score.toLocaleString();
}

/**
 * Format time duration for display
 * @param milliseconds Time in milliseconds
 * @returns Formatted time string
 */
export function formatTime(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  
  if (minutes > 0) {
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  }
  
  return `${seconds}s`;
}

/**
 * Generate a random location within bounds (for testing)
 * @returns Random location
 */
export function generateRandomLocation(): { lat: number; lng: number } {
  return {
    lat: (Math.random() - 0.5) * 180, // -90 to 90
    lng: (Math.random() - 0.5) * 360, // -180 to 180
  };
}

/**
 * Validate if coordinates are valid
 * @param lat Latitude
 * @param lng Longitude
 * @returns True if valid coordinates
 */
export function isValidCoordinates(lat: number, lng: number): boolean {
  return lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;
}
