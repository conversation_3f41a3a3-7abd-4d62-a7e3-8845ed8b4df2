import React from 'react';
import Button from '@/components/ui/Button';

interface HeroSectionProps {
  onPlayNow: () => void;
}

export default function HeroSection({ onPlayNow }: HeroSectionProps) {
  return (
    <div className="text-center py-16 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Logo/Title */}
        <h1 className="text-6xl md:text-7xl font-bold text-gray-800 mb-4">
          Geo-<PERSON>ser
        </h1>
        
        {/* Tagline */}
        <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-2xl mx-auto">
          Test your geographical knowledge. Explore the world through Street View and guess where you are.
        </p>
        
        {/* Features */}
        <div className="flex flex-wrap justify-center gap-6 mb-12 text-gray-700">
          <div className="flex items-center gap-2">
            <span className="text-blue-600">🌍</span>
            <span>5 Rounds</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-blue-600">📍</span>
            <span>Distance-based Scoring</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-blue-600">🏆</span>
            <span>Multiple Categories</span>
          </div>
        </div>
        
        {/* CTA Button */}
        <Button 
          onClick={onPlayNow}
          size="lg"
          className="text-xl px-8 py-4 shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          Play Now
        </Button>
        
        {/* Optional Login Links */}
        <div className="mt-8 text-sm text-gray-500">
          <span>Want to save your scores? </span>
          <button className="text-blue-600 hover:text-blue-800 underline">
            Sign Up
          </button>
          <span className="mx-2">or</span>
          <button className="text-blue-600 hover:text-blue-800 underline">
            Log In
          </button>
        </div>
      </div>
    </div>
  );
}
