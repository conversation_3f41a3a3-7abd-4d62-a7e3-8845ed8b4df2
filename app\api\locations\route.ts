import { NextRequest, NextResponse } from 'next/server';
import { generateRandomStreetViewLocation } from '@/lib/googleMaps';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    
    // For now, we'll return a random location regardless of category
    // In a real implementation, you'd filter based on the category
    const location = generateRandomStreetViewLocation();
    
    return NextResponse.json({
      success: true,
      data: {
        location,
        categoryId,
        // In a real implementation, you might include additional metadata
        metadata: {
          country: 'Unknown',
          region: 'Unknown',
        }
      }
    });
  } catch (error) {
    console.error('Error generating location:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Failed to generate location',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
